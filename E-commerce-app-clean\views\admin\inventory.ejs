<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Inventory Management - Admin Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="/css/admin.css">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
    body {
      background-color: #f8f9fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
    }

    .admin-header {
      background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
      color: white;
      padding: 2rem 0;
      margin-bottom: 2rem;
    }

    .stats-card {
      background: white;
      border-radius: 15px;
      padding: 1.5rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-bottom: 1rem;
      transition: transform 0.3s ease;
    }

    .stats-card:hover {
      transform: translateY(-5px);
    }

    .stats-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      color: white;
    }

    .search-filter-section {
      background: white;
      border-radius: 15px;
      padding: 2rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
    }

    .product-card {
      background: white;
      border-radius: 15px;
      padding: 1.5rem;
      margin-bottom: 1rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
    }

    .product-card:hover {
      transform: translateY(-2px);
    }

    .product-image {
      width: 80px;
      height: 80px;
      object-fit: cover;
      border-radius: 8px;
    }

    .stock-badge {
      padding: 0.5rem 1rem;
      border-radius: 25px;
      font-weight: 600;
      font-size: 0.8rem;
      text-transform: uppercase;
    }

    .stock-available { background-color: #d4edda; color: #155724; }
    .stock-low { background-color: #fff3cd; color: #856404; }
    .stock-out { background-color: #f8d7da; color: #721c24; }

    .action-btn {
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      text-decoration: none;
      display: inline-block;
      margin: 0.2rem;
      transition: all 0.3s ease;
    }

    .btn-edit { background: #007bff; color: white; }
    .btn-edit:hover { background: #0056b3; color: white; }

    .btn-stock { background: #28a745; color: white; }
    .btn-stock:hover { background: #218838; color: white; }

    .btn-delete { background: #dc3545; color: white; }
    .btn-delete:hover { background: #c82333; color: white; }

    .stock-input {
      width: 80px;
      display: inline-block;
      margin: 0 0.5rem;
    }

    .stock-controls {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-top: 1rem;
    }

    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 2rem;
    }

    .clear-btn {
      background: #6c757d;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      margin-left: 0.5rem;
    }

    .clear-btn:hover {
      background: #5a6268;
    }

    .bulk-actions {
      background: white;
      border-radius: 15px;
      padding: 1rem;
      margin-bottom: 2rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .alert-link {
      text-decoration: none;
      color: inherit;
    }

    .alert-link:hover {
      color: inherit;
      text-decoration: underline;
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
      .admin-header .col-md-6 {
        text-align: center !important;
        margin-bottom: 1rem;
      }

      .search-filter-section .col-md-3,
      .search-filter-section .col-md-2,
      .search-filter-section .col-md-1 {
        margin-bottom: 1rem;
      }

      .product-card .col-md-1,
      .product-card .col-md-2,
      .product-card .col-md-3 {
        margin-bottom: 0.5rem;
      }

      .stock-controls {
        flex-wrap: wrap;
        gap: 0.25rem;
      }

      .stock-controls .btn {
        flex: 1;
        min-width: auto;
      }

      .action-btn {
        display: block;
        width: 100%;
        margin-bottom: 0.25rem;
      }
    }

    @media (max-width: 576px) {
      .stats-card {
        margin-bottom: 1rem;
      }

      .product-card {
        padding: 1rem;
      }

      .product-card .row {
        text-align: center;
      }

      .stock-input {
        max-width: 80px;
        margin: 0 auto;
      }
    }
  </style>
</head>
<body>
  <!-- Include Admin Sidebar -->
  <%- include('./partials/sidebar', { page: 'inventory' }) %>

  <div class="main-content">
    <!-- Admin Header -->
    <div class="admin-header">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-md-6">
            <h1><i class="fas fa-boxes me-3"></i>Inventory Management</h1>
            <p class="mb-0">Manage product stock and inventory</p>
          </div>
          <div class="col-md-6 text-end">
            <a href="/admin/inventory/alerts" class="btn btn-warning me-2">
              <i class="fas fa-exclamation-triangle me-2"></i>Stock Alerts
              <% if (stats.lowStockCount > 0 || stats.outOfStockCount > 0) { %>
                <span class="badge bg-danger ms-2"><%= stats.lowStockCount + stats.outOfStockCount %></span>
              <% } %>
            </a>
            <a href="/admin/inventory/movements" class="btn btn-light">
              <i class="fas fa-history me-2"></i>Stock Movements
            </a>
          </div>
        </div>
      </div>
    </div>

    <div class="container">
    <!-- Inventory Statistics -->
    <div class="row mb-4">
      <div class="col-md-2 col-sm-4 col-6">
        <div class="stats-card text-center">
          <div class="stats-icon mx-auto mb-2" style="background: #007bff;">
            <i class="fas fa-boxes"></i>
          </div>
          <h4><%= stats.totalProducts %></h4>
          <p class="mb-0">Total Products</p>
        </div>
      </div>
      <div class="col-md-2 col-sm-4 col-6">
        <div class="stats-card text-center">
          <div class="stats-icon mx-auto mb-2" style="background: #28a745;">
            <i class="fas fa-cubes"></i>
          </div>
          <h4><%= stats.totalStock %></h4>
          <p class="mb-0">Total Stock</p>
        </div>
      </div>
      <div class="col-md-2 col-sm-4 col-6">
        <div class="stats-card text-center">
          <div class="stats-icon mx-auto mb-2" style="background: #ffc107;">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <h4><%= stats.lowStockCount %></h4>
          <p class="mb-0">Low Stock</p>
        </div>
      </div>
      <div class="col-md-2 col-sm-4 col-6">
        <div class="stats-card text-center">
          <div class="stats-icon mx-auto mb-2" style="background: #dc3545;">
            <i class="fas fa-times-circle"></i>
          </div>
          <h4><%= stats.outOfStockCount %></h4>
          <p class="mb-0">Out of Stock</p>
        </div>
      </div>
      <div class="col-md-4 col-sm-8 col-12">
        <div class="stats-card text-center">
          <div class="stats-icon mx-auto mb-2" style="background: #6f42c1;">
            <i class="fas fa-rupee-sign"></i>
          </div>
          <h4>₹<%= (stats.totalValue || 0).toFixed(0) %></h4>
          <p class="mb-0">Total Inventory Value</p>
        </div>
      </div>
    </div>

    <!-- Stock Alerts -->
    <% if (stats.lowStockCount > 0 || stats.outOfStockCount > 0) { %>
      <div class="alert alert-warning" role="alert">
        <h5 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>Stock Alerts</h5>
        <p class="mb-0">
          <% if (stats.outOfStockCount > 0) { %>
            <strong><%= stats.outOfStockCount %></strong> products are out of stock.
          <% } %>
          <% if (stats.lowStockCount > 0) { %>
            <strong><%= stats.lowStockCount %></strong> products have low stock (≤10 units).
          <% } %>
          <a href="/admin/inventory/alerts" class="alert-link">View details →</a>
        </p>
      </div>
    <% } %>

    <!-- Search and Filter Section -->
    <div class="search-filter-section">
      <form method="GET" action="/admin/inventory" class="row g-3">
        <div class="col-md-3">
          <label class="form-label">Search Products</label>
          <input type="text" class="form-control" name="search" value="<%= search %>"
                 placeholder="Product name, brand, or description...">
        </div>
        <div class="col-md-2">
          <label class="form-label">Category</label>
          <select class="form-select" name="category">
            <option value="">All Categories</option>
            <% categories.forEach(cat => { %>
              <option value="<%= cat.name %>" <%= category === cat.name ? 'selected' : '' %>><%= cat.name %></option>
            <% }); %>
          </select>
        </div>
        <div class="col-md-2">
          <label class="form-label">Stock Status</label>
          <select class="form-select" name="stockStatus">
            <option value="">All Stock</option>
            <option value="available" <%= stockStatus === 'available' ? 'selected' : '' %>>Available</option>
            <option value="low" <%= stockStatus === 'low' ? 'selected' : '' %>>Low Stock</option>
            <option value="out" <%= stockStatus === 'out' ? 'selected' : '' %>>Out of Stock</option>
          </select>
        </div>
        <div class="col-md-2">
          <label class="form-label">Sort By</label>
          <select class="form-select" name="sortBy">
            <option value="createdAt" <%= sortBy === 'createdAt' ? 'selected' : '' %>>Date Added</option>
            <option value="productName" <%= sortBy === 'productName' ? 'selected' : '' %>>Product Name</option>
            <option value="quantity" <%= sortBy === 'quantity' ? 'selected' : '' %>>Stock Quantity</option>
            <option value="salePrice" <%= sortBy === 'salePrice' ? 'selected' : '' %>>Price</option>
          </select>
        </div>
        <div class="col-md-1">
          <label class="form-label">Order</label>
          <select class="form-select" name="sortOrder">
            <option value="desc" <%= sortOrder === 'desc' ? 'selected' : '' %>>Desc</option>
            <option value="asc" <%= sortOrder === 'asc' ? 'selected' : '' %>>Asc</option>
          </select>
        </div>
        <div class="col-md-2">
          <label class="form-label">&nbsp;</label>
          <div class="d-flex">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-search me-1"></i>Search
            </button>
            <a href="/admin/inventory" class="clear-btn">
              <i class="fas fa-times me-1"></i>Clear
            </a>
          </div>
        </div>
      </form>
    </div>

    <!-- Bulk Actions -->
    <div class="bulk-actions">
      <div class="row align-items-center">
        <div class="col-md-6">
          <h6 class="mb-0"><i class="fas fa-tasks me-2"></i>Bulk Actions</h6>
        </div>
        <div class="col-md-6 text-end">
          <button class="btn btn-outline-primary btn-sm" onclick="selectAllProducts()">
            <i class="fas fa-check-square me-1"></i>Select All
          </button>
          <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
            <i class="fas fa-times me-1"></i>Clear Selection
          </button>
          <button class="btn btn-success btn-sm" onclick="showBulkUpdateModal()" disabled id="bulkUpdateBtn">
            <i class="fas fa-edit me-1"></i>Bulk Update Stock
          </button>
        </div>
      </div>
    </div>

    <!-- Products List -->
    <% if (products && products.length > 0) { %>
      <% products.forEach(product => { %>
        <div class="product-card">
          <div class="row align-items-center">
            <div class="col-md-1">
              <input type="checkbox" class="form-check-input product-checkbox"
                     value="<%= product._id %>" onchange="updateBulkActions()">
            </div>
            <div class="col-md-1">
              <% if (product.productImage && product.productImage.length > 0) { %>
                <img src="/uploads/product-images/<%= product.productImage[0] %>"
                     alt="<%= product.productName %>" class="product-image">
              <% } else { %>
                <div class="product-image bg-light d-flex align-items-center justify-content-center">
                  <i class="fas fa-image text-muted"></i>
                </div>
              <% } %>
            </div>
            <div class="col-md-3">
              <h6 class="mb-1"><%= product.productName || 'Unknown Product' %></h6>
              <p class="text-muted mb-1">
                <% if (product.category) { %>
                  <%= product.category.name %>
                <% } else { %>
                  No Category
                <% } %>
              </p>
              <small class="text-muted">₹<%= (product.salePrice || product.regularPrice || 0).toFixed(2) %></small>
            </div>
            <div class="col-md-2">
              <h6 class="mb-1">Current Stock</h6>
              <div class="d-flex align-items-center">
                <%
                  const safeQuantity = product.quantity || 0;
                %>
                <span class="fw-bold me-2"><%= safeQuantity %></span>
                <span class="stock-badge <%= safeQuantity === 0 ? 'stock-out' :
                                           safeQuantity <= 10 ? 'stock-low' : 'stock-available' %>">
                  <%= safeQuantity === 0 ? 'Out of Stock' :
                      safeQuantity <= 10 ? 'Low Stock' : 'Available' %>
                </span>
              </div>
            </div>
            <div class="col-md-3">
              <div class="stock-controls">
                <button class="btn btn-sm btn-outline-danger" onclick="updateStock('<%= product._id %>', 'subtract')">
                  <i class="fas fa-minus"></i>
                </button>
                <input type="number" class="form-control stock-input"
                       id="stock-<%= product._id %>" value="1" min="1">
                <button class="btn btn-sm btn-outline-success" onclick="updateStock('<%= product._id %>', 'add')">
                  <i class="fas fa-plus"></i>
                </button>
                <button class="btn btn-sm btn-primary" onclick="setStock('<%= product._id %>')">
                  <i class="fas fa-edit"></i>
                </button>
              </div>
            </div>
            <div class="col-md-2 text-end">
              <a href="/admin/edit-product/<%= product._id %>" class="action-btn btn-edit">
                <i class="fas fa-edit me-1"></i>Edit
              </a>
              <button class="action-btn btn-delete" onclick="deleteProduct('<%= product._id %>', '<%= product.productName %>')">
                <i class="fas fa-trash me-1"></i>Delete
              </button>
            </div>
          </div>
        </div>
      <% }); %>

      <!-- Pagination -->
      <% if (totalPages > 1) { %>
        <div class="pagination-container">
          <nav aria-label="Products pagination">
            <ul class="pagination">
              <% if (currentPage > 1) { %>
                <li class="page-item">
                  <a class="page-link" href="?page=<%= currentPage - 1 %><%= search ? '&search=' + search : '' %><%= category ? '&category=' + category : '' %><%= stockStatus ? '&stockStatus=' + stockStatus : '' %><%= sortBy ? '&sortBy=' + sortBy : '' %><%= sortOrder ? '&sortOrder=' + sortOrder : '' %>">
                    <i class="fas fa-chevron-left"></i>
                  </a>
                </li>
              <% } %>

              <% for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) { %>
                <li class="page-item <%= i === currentPage ? 'active' : '' %>">
                  <a class="page-link" href="?page=<%= i %><%= search ? '&search=' + search : '' %><%= category ? '&category=' + category : '' %><%= stockStatus ? '&stockStatus=' + stockStatus : '' %><%= sortBy ? '&sortBy=' + sortBy : '' %><%= sortOrder ? '&sortOrder=' + sortOrder : '' %>">
                    <%= i %>
                  </a>
                </li>
              <% } %>

              <% if (currentPage < totalPages) { %>
                <li class="page-item">
                  <a class="page-link" href="?page=<%= currentPage + 1 %><%= search ? '&search=' + search : '' %><%= category ? '&category=' + category : '' %><%= stockStatus ? '&stockStatus=' + stockStatus : '' %><%= sortBy ? '&sortBy=' + sortBy : '' %><%= sortOrder ? '&sortOrder=' + sortOrder : '' %>">
                    <i class="fas fa-chevron-right"></i>
                  </a>
                </li>
              <% } %>
            </ul>
          </nav>
        </div>
      <% } %>

    <% } else { %>
      <!-- Empty State -->
      <div class="product-card text-center">
        <div class="py-5">
          <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
          <h3>No Products Found</h3>
          <p class="text-muted">
            <% if (search || category || stockStatus) { %>
              No products match your search criteria. Try adjusting your filters.
            <% } else { %>
              No products have been added yet.
            <% } %>
          </p>
          <a href="/admin/add-products" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Add New Product
          </a>
        </div>
      </div>
    <% } %>
  </div>

  <!-- Set Stock Modal -->
  <div class="modal fade" id="setStockModal" tabindex="-1" aria-labelledby="setStockModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="setStockModalLabel">Set Stock Quantity</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <p>Set stock quantity for <strong id="setStockProductName"></strong>?</p>
          <div class="mb-3">
            <label for="newStockQuantity" class="form-label">New Quantity:</label>
            <input type="number" class="form-control" id="newStockQuantity" min="0" value="0">
          </div>
          <div class="mb-3">
            <small class="text-muted">Current quantity: <span id="currentStockQuantity"></span></small>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary" id="confirmSetStock">Set Stock</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Bulk Update Modal -->
  <div class="modal fade" id="bulkUpdateModal" tabindex="-1" aria-labelledby="bulkUpdateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="bulkUpdateModalLabel">Bulk Stock Update</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <p>Update stock for <strong id="selectedProductsCount"></strong> selected products?</p>
          <div class="row g-3">
            <div class="col-md-6">
              <label for="bulkAction" class="form-label">Action:</label>
              <select class="form-select" id="bulkAction">
                <option value="add">Add to Stock</option>
                <option value="subtract">Subtract from Stock</option>
                <option value="set">Set Stock to</option>
              </select>
            </div>
            <div class="col-md-6">
              <label for="bulkQuantity" class="form-label">Quantity:</label>
              <input type="number" class="form-control" id="bulkQuantity" min="0" value="1">
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-success" id="confirmBulkUpdate">Update Stock</button>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    let currentProductId = null;

    // Update stock (add/subtract)
    async function updateStock(productId, action) {
      const quantityInput = document.getElementById(`stock-${productId}`);
      const quantity = parseInt(quantityInput.value);

      if (!quantity || quantity <= 0) {
        Swal.fire({
          icon: 'warning',
          title: 'Invalid Quantity',
          text: 'Please enter a valid quantity.',
          confirmButtonColor: '#28a745'
        });
        return;
      }

      try {
        const response = await fetch(`/admin/inventory/${productId}/stock`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ quantity, action })
        });

        const result = await response.json();

        if (result.success) {
          Swal.fire({
            icon: 'success',
            title: 'Stock Updated',
            text: result.message,
            confirmButtonColor: '#28a745'
          }).then(() => {
            location.reload();
          });
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Update Failed',
            text: result.message,
            confirmButtonColor: '#dc3545'
          });
        }
      } catch (error) {
        console.error('Error updating stock:', error);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to update stock. Please try again.',
          confirmButtonColor: '#dc3545'
        });
      }
    }

    // Set stock quantity
    function setStock(productId) {
      currentProductId = productId;

      // Get current product info
      const productCard = document.querySelector(`input[value="${productId}"]`).closest('.product-card');
      const productName = productCard.querySelector('h6').textContent;
      const currentQuantity = productCard.querySelector('.fw-bold').textContent;

      document.getElementById('setStockProductName').textContent = productName;
      document.getElementById('currentStockQuantity').textContent = currentQuantity;
      document.getElementById('newStockQuantity').value = currentQuantity;

      new bootstrap.Modal(document.getElementById('setStockModal')).show();
    }

    // Confirm set stock
    document.getElementById('confirmSetStock').addEventListener('click', async function() {
      const quantity = parseInt(document.getElementById('newStockQuantity').value);

      if (quantity < 0) {
        Swal.fire({
          icon: 'warning',
          title: 'Invalid Quantity',
          text: 'Quantity cannot be negative.',
          confirmButtonColor: '#28a745'
        });
        return;
      }

      try {
        const response = await fetch(`/admin/inventory/${currentProductId}/stock`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ quantity, action: 'set' })
        });

        const result = await response.json();

        if (result.success) {
          Swal.fire({
            icon: 'success',
            title: 'Stock Set',
            text: result.message,
            confirmButtonColor: '#28a745'
          }).then(() => {
            location.reload();
          });
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Update Failed',
            text: result.message,
            confirmButtonColor: '#dc3545'
          });
        }
      } catch (error) {
        console.error('Error setting stock:', error);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to set stock. Please try again.',
          confirmButtonColor: '#dc3545'
        });
      }

      bootstrap.Modal.getInstance(document.getElementById('setStockModal')).hide();
    });

    // Delete product
    function deleteProduct(productId, productName) {
      Swal.fire({
        title: 'Delete Product',
        text: `Are you sure you want to delete "${productName}"?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Yes, delete it!'
      }).then(async (result) => {
        if (result.isConfirmed) {
          try {
            const response = await fetch('/admin/delete-product', {
              method: 'DELETE',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ productId })
            });

            const deleteResult = await response.json();

            if (deleteResult.success) {
              Swal.fire({
                icon: 'success',
                title: 'Deleted!',
                text: 'Product has been deleted.',
                confirmButtonColor: '#28a745'
              }).then(() => {
                location.reload();
              });
            } else {
              Swal.fire({
                icon: 'error',
                title: 'Delete Failed',
                text: deleteResult.message,
                confirmButtonColor: '#dc3545'
              });
            }
          } catch (error) {
            console.error('Error deleting product:', error);
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: 'Failed to delete product. Please try again.',
              confirmButtonColor: '#dc3545'
            });
          }
        }
      });
    }

    // Bulk actions
    function selectAllProducts() {
      const checkboxes = document.querySelectorAll('.product-checkbox');
      checkboxes.forEach(checkbox => {
        checkbox.checked = true;
      });
      updateBulkActions();
    }

    function clearSelection() {
      const checkboxes = document.querySelectorAll('.product-checkbox');
      checkboxes.forEach(checkbox => {
        checkbox.checked = false;
      });
      updateBulkActions();
    }

    function updateBulkActions() {
      const selectedCheckboxes = document.querySelectorAll('.product-checkbox:checked');
      const bulkUpdateBtn = document.getElementById('bulkUpdateBtn');

      if (selectedCheckboxes.length > 0) {
        bulkUpdateBtn.disabled = false;
        bulkUpdateBtn.innerHTML = `<i class="fas fa-edit me-1"></i>Bulk Update Stock (${selectedCheckboxes.length})`;
      } else {
        bulkUpdateBtn.disabled = true;
        bulkUpdateBtn.innerHTML = '<i class="fas fa-edit me-1"></i>Bulk Update Stock';
      }
    }

    function showBulkUpdateModal() {
      const selectedCheckboxes = document.querySelectorAll('.product-checkbox:checked');

      if (selectedCheckboxes.length === 0) {
        Swal.fire({
          icon: 'warning',
          title: 'No Products Selected',
          text: 'Please select products to update.',
          confirmButtonColor: '#28a745'
        });
        return;
      }

      document.getElementById('selectedProductsCount').textContent = selectedCheckboxes.length;
      new bootstrap.Modal(document.getElementById('bulkUpdateModal')).show();
    }

    // Confirm bulk update
    document.getElementById('confirmBulkUpdate').addEventListener('click', async function() {
      const selectedCheckboxes = document.querySelectorAll('.product-checkbox:checked');
      const action = document.getElementById('bulkAction').value;
      const quantity = parseInt(document.getElementById('bulkQuantity').value);

      if (quantity < 0) {
        Swal.fire({
          icon: 'warning',
          title: 'Invalid Quantity',
          text: 'Quantity cannot be negative.',
          confirmButtonColor: '#28a745'
        });
        return;
      }

      const updates = Array.from(selectedCheckboxes).map(checkbox => ({
        productId: checkbox.value,
        quantity: quantity,
        action: action
      }));

      try {
        const response = await fetch('/admin/inventory/bulk-update', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ updates })
        });

        const result = await response.json();

        if (result.success) {
          let message = result.message;
          if (result.errors && result.errors.length > 0) {
            message += `\n\nErrors: ${result.errors.join(', ')}`;
          }

          Swal.fire({
            icon: 'success',
            title: 'Bulk Update Completed',
            text: message,
            confirmButtonColor: '#28a745'
          }).then(() => {
            location.reload();
          });
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Bulk Update Failed',
            text: result.message,
            confirmButtonColor: '#dc3545'
          });
        }
      } catch (error) {
        console.error('Error in bulk update:', error);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to perform bulk update. Please try again.',
          confirmButtonColor: '#dc3545'
        });
      }

      bootstrap.Modal.getInstance(document.getElementById('bulkUpdateModal')).hide();
    });
  </script>
    </div> <!-- End container -->
  </div> <!-- End main-content -->
</body>
</html>
