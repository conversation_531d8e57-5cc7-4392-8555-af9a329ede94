<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Edit Address | Luxe Scents</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    body {
      background-color: #f8f9fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .navbar {
      background-color: #000000;
      padding: 1rem 2rem;
      border-bottom: 1px solid #090909;
    }
    .navbar-brand {
      font-weight: 700;
      font-size: 1.8rem;
      color: #ffffff !important;
      letter-spacing: 1px;
    }
    .nav-link {
      color: #e0e0e0 !important;
      font-weight: 500;
      margin: 0 1rem;
      transition: color 0.3s;
    }
    .nav-link:hover {
      color: #bb86fc !important;
    }
    .form-container {
      max-width: 800px;
      margin: 2rem auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    .form-header {
      background: linear-gradient(135deg, #6200ea, #bb86fc);
      color: white;
      padding: 2rem;
      text-align: center;
    }
    .form-body {
      padding: 2rem;
    }
    .form-section {
      margin-bottom: 2rem;
    }
    .form-section h5 {
      color: #6200ea;
      border-bottom: 2px solid #6200ea;
      padding-bottom: 0.5rem;
      margin-bottom: 1rem;
    }
    .form-control {
      border-radius: 10px;
      border: 2px solid #e9ecef;
      padding: 0.75rem 1rem;
      transition: all 0.3s;
    }
    .form-control:focus {
      border-color: #6200ea;
      box-shadow: 0 0 0 0.2rem rgba(98, 0, 234, 0.25);
    }
    .form-select {
      border-radius: 10px;
      border: 2px solid #e9ecef;
      padding: 0.75rem 1rem;
      transition: all 0.3s;
    }
    .form-select:focus {
      border-color: #6200ea;
      box-shadow: 0 0 0 0.2rem rgba(98, 0, 234, 0.25);
    }
    .btn-primary {
      background-color: #6200ea;
      border-color: #6200ea;
      border-radius: 25px;
      padding: 0.75rem 2rem;
      font-weight: 500;
      transition: all 0.3s;
    }
    .btn-primary:hover {
      background-color: #5300d1;
      transform: translateY(-2px);
    }
    .btn-secondary {
      border-radius: 25px;
      padding: 0.75rem 2rem;
      font-weight: 500;
      transition: all 0.3s;
    }
    .btn-secondary:hover {
      transform: translateY(-2px);
    }
    .alert {
      border-radius: 10px;
      border: none;
    }
    .form-check-input:checked {
      background-color: #6200ea;
      border-color: #6200ea;
    }
    .form-check-input:focus {
      box-shadow: 0 0 0 0.2rem rgba(98, 0, 234, 0.25);
    }
    .action-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      margin-top: 2rem;
    }
    @media (max-width: 768px) {
      .form-container {
        margin: 1rem;
      }
      .action-buttons {
        flex-direction: column;
      }
    }
  </style>
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
      <a class="navbar-brand" href="/">LUXE SCENTS</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <a class="nav-link" href="/">Home</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/shop">Shop</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/profile">Profile</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/addresses">Addresses</a>
          </li>
        </ul>
        <div class="navbar-icons">
          <div class="dropdown">
            <a href="#" class="dropdown-toggle text-white" role="button" data-bs-toggle="dropdown">
              <i class="fas fa-user-circle"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
              <li><a class="dropdown-item" href="/profile">My Profile</a></li>
              <li><a class="dropdown-item" href="/addresses">My Addresses</a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="/logout">Logout</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- Form Container -->
  <div class="form-container">
    <!-- Header -->
    <div class="form-header">
      <h2><i class="fas fa-edit me-2"></i>Edit Address</h2>
      <p class="mb-0">Update your delivery address information</p>
    </div>

    <!-- Body -->
    <div class="form-body">
      <!-- Success/Error Messages -->
      <% if (message) { %>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
          <i class="fas fa-check-circle me-2"></i><%= message %>
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      <% } %>
      <% if (error) { %>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
          <i class="fas fa-exclamation-circle me-2"></i><%= error %>
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      <% } %>

      <form action="/addresses/edit/<%= address._id %>" method="POST" id="editAddressForm">
        <!-- Address Title & Type -->
        <div class="form-section">
          <h5><i class="fas fa-tag me-2"></i>Address Details</h5>
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="title" class="form-label">Address Title *</label>
              <input type="text" class="form-control" id="title" name="title" required placeholder="e.g., Home, Office" value="<%= address.title %>">
            </div>
            <div class="col-md-6 mb-3">
              <label for="addressType" class="form-label">Address Type</label>
              <select class="form-select" id="addressType" name="addressType">
                <option value="Home" <%= address.addressType === 'Home' ? 'selected' : '' %>>Home</option>
                <option value="Work" <%= address.addressType === 'Work' ? 'selected' : '' %>>Work</option>
                <option value="Other" <%= address.addressType === 'Other' ? 'selected' : '' %>>Other</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Contact Information -->
        <div class="form-section">
          <h5><i class="fas fa-user me-2"></i>Contact Information</h5>
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="fullName" class="form-label">Full Name *</label>
              <input type="text" class="form-control" id="fullName" name="fullName" required placeholder="Enter full name" value="<%= address.fullName %>">
            </div>
            <div class="col-md-6 mb-3">
              <label for="phone" class="form-label">Phone Number *</label>
              <input type="tel" class="form-control" id="phone" name="phone" required placeholder="+91XXXXXXXXXX" value="<%= address.phone %>">
            </div>
          </div>
        </div>

        <!-- Address Information -->
        <div class="form-section">
          <h5><i class="fas fa-map-marker-alt me-2"></i>Address Information</h5>
          <div class="mb-3">
            <label for="street" class="form-label">Street Address *</label>
            <textarea class="form-control" id="street" name="street" rows="2" required placeholder="House/Flat number, Building name, Street name"><%= address.street %></textarea>
          </div>
          <div class="mb-3">
            <label for="landmark" class="form-label">Landmark (Optional)</label>
            <input type="text" class="form-control" id="landmark" name="landmark" placeholder="Near landmark or area" value="<%= address.landmark || '' %>">
          </div>
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="city" class="form-label">City *</label>
              <input type="text" class="form-control" id="city" name="city" required placeholder="Enter city" value="<%= address.city %>">
            </div>
            <div class="col-md-6 mb-3">
              <label for="state" class="form-label">State *</label>
              <input type="text" class="form-control" id="state" name="state" required placeholder="Enter state" value="<%= address.state %>">
            </div>
          </div>
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="zipCode" class="form-label">ZIP Code *</label>
              <input type="text" class="form-control" id="zipCode" name="zipCode" required placeholder="6-digit ZIP code" maxlength="6" value="<%= address.zipCode %>">
            </div>
            <div class="col-md-6 mb-3">
              <label for="country" class="form-label">Country</label>
              <input type="text" class="form-control" id="country" name="country" value="<%= address.country %>" readonly>
            </div>
          </div>
        </div>

        <!-- Default Address -->
        <div class="form-section">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="isDefault" name="isDefault" <%= address.isDefault ? 'checked' : '' %>>
            <label class="form-check-label" for="isDefault">
              <strong>Set as default address</strong>
              <small class="text-muted d-block">This address will be used as your primary delivery address</small>
            </label>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save me-2"></i>Update Address
          </button>
          <a href="/addresses" class="btn btn-secondary">
            <i class="fas fa-times me-2"></i>Cancel
          </a>
        </div>
      </form>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Phone number formatting
    document.getElementById('phone').addEventListener('input', function(e) {
      let value = e.target.value.replace(/\D/g, '');
      if (value.length > 0 && !value.startsWith('91')) {
        if (value.length <= 10) {
          value = '91' + value;
        }
      }
      if (value.length > 12) {
        value = value.substring(0, 12);
      }
      e.target.value = value.length > 0 ? '+' + value : '';
    });

    // ZIP code validation
    document.getElementById('zipCode').addEventListener('input', function(e) {
      e.target.value = e.target.value.replace(/\D/g, '').substring(0, 6);
    });

    // Form validation
    document.getElementById('editAddressForm').addEventListener('submit', function(e) {
      const phone = document.getElementById('phone').value;
      const zipCode = document.getElementById('zipCode').value;

      // Validate phone number
      if (!/^\+91\d{10}$/.test(phone)) {
        e.preventDefault();
        alert('Please enter a valid phone number in format +91XXXXXXXXXX');
        return;
      }

      // Validate ZIP code
      if (!/^\d{6}$/.test(zipCode)) {
        e.preventDefault();
        alert('Please enter a valid 6-digit ZIP code');
        return;
      }
    });
  </script>
</body>
</html>
