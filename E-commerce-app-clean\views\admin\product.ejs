<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product List</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/admin.css">
    <style>
        .product-img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
        }
        .status-available {
            color: #198754;
        }
        .status-out-of-stock {
            color: #dc3545;
        }
        .status-discontinued {
            color: #6c757d;
        }
        .action-btns {
            white-space: nowrap;
        }
        /* Pagination styles */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 10px;
        }
        .pagination-btn {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #e0e0e0;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }
        .pagination-btn:hover:not(.disabled) {
            background-color: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }
        .pagination-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .pagination-btn.active {
            background-color: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }
    </style>
</head>
<body>
    <!-- Include Admin Sidebar -->
    <%- include('partials/sidebar', { page: 'products' }) %>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid mt-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Product Management</h2>
                <a href="/admin/add-products" class="btn btn-primary">
                    <i class="bi bi-plus-lg"></i> Add Product
                </a>
            </div>

            <% if (error) { %>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <%= error %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <% } %>

            <% if (typeof message !== 'undefined' && message) { %>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <%= message %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <% } %>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Image</th>
                                    <th>Name</th>
                                    <th>Category</th>
                                    <th>Price</th>
                                    <th>Quantity</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="productTableBody">
                                <% if (products.length === 0) { %>
                                    <tr>
                                        <td colspan="7" style="text-align: center;">No products found</td>
                                    </tr>
                                <% } else { %>
                                    <% products.forEach(product => { %>
                                        <tr>
                                            <td>
                                                <% if (product.productImage && product.productImage.length > 0) { %>
                                                    <img src="/uploads/product-images/<%= product.productImage[0] %>" class="product-img" alt="<%= product.productName %>">
                                                <% } else { %>
                                                    <div class="product-img bg-light d-flex align-items-center justify-content-center">
                                                        <i class="bi bi-image text-muted"></i>
                                                    </div>
                                                <% } %>
                                            </td>
                                            <td><%= product.productName || 'N/A' %></td>
                                            <td><%= product.category && product.category.name ? product.category.name : 'N/A' %></td>
                                            <td>$<%= product.price ? product.price.toFixed(2) : '0.00' %></td>
                                            <td><%= product.quantity ?? '0' %></td>
                                            <td>
                                                <span class="status-<%= product.status.toLowerCase().replace(' ', '-') %>">
                                                    <i class="bi bi-circle-fill"></i> <%= product.status %>
                                                </span>
                                                <% if (product.isBlocked) { %>
                                                    <span class="badge bg-danger ms-2">Blocked (Hidden from users)</span>
                                                <% } %>
                                            </td>
                                            <td class="action-btns">
                                                <a href="/admin/edit-product/<%= product._id %>" class="btn btn-sm btn-outline-primary me-1">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <a href="/admin/block-product/<%= product._id %>" class="btn btn-sm <%= product.isBlocked ? 'btn-success' : 'btn-warning' %> me-1" title="<%= product.isBlocked ? 'Unblock Product' : 'Block Product' %>">
                                                    <i class="bi <%= product.isBlocked ? 'bi-check-circle' : 'bi-slash-circle' %>"></i>
                                                    <%= product.isBlocked ? 'Unblock' : 'Block' %>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteProduct('<%= product._id %>')">
                                                    <i class="bi bi-trash"></i> Delete
                                                </button>
                                            </td>
                                        </tr>
                                    <% }) %>
                                <% } %>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Pagination Controls -->
            <% if (totalPages > 1 && typeof currentPage !== 'undefined') { %>
                <div class="pagination" id="paginationControls">
                    <button class="pagination-btn <%= currentPage === 1 ? 'disabled' : '' %>"
                            onclick="loadPage(<%= Math.max(1, currentPage - 1) %>)"
                            <% if (currentPage === 1) { %> disabled <% } %>>
                        Previous
                    </button>

                    <% for (let i = 1; i <= totalPages; i++) { %>
                        <button class="pagination-btn <%= currentPage === i ? 'active' : '' %>"
                                onclick="loadPage(<%= i %>)">
                            <%= i %>
                        </button>
                    <% } %>

                    <button class="pagination-btn <%= currentPage === totalPages ? 'disabled' : '' %>"
                            onclick="loadPage(<%= Math.min(totalPages, currentPage + 1) %>)"
                            <% if (currentPage === totalPages) { %> disabled <% } %>>
                        Next
                    </button>
                </div>
            <% } else if (typeof currentPage === 'undefined') { %>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    Error: Unable to load pagination. Please try again.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <% } %>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Validation function for product name (letters, spaces, and numbers allowed)
        function validateProductName(name) {
            const pattern = /^[A-Za-z0-9\s]+$/;
            return pattern.test(name);
        }

        // Validate displayed product names on page load
        document.addEventListener('DOMContentLoaded', () => {
            const productNameCells = document.querySelectorAll('#productTableBody td:nth-child(2)');
            productNameCells.forEach(cell => {
                const name = cell.textContent.trim();
                if (!validateProductName(name)) {
                    cell.textContent = 'Invalid Name';
                    cell.style.color = 'red';
                }
            });
        });

        // Load new page via AJAX
        async function loadPage(page) {
            try {
                if (!Number.isInteger(page) || page < 1) {
                    throw new Error('Invalid page number');
                }

                const paginationButtons = document.querySelectorAll('.pagination-btn');
                paginationButtons.forEach(btn => {
                    btn.classList.add('disabled');
                    btn.disabled = true;
                });

                const response = await fetch(`/admin/products?page=${page}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    },
                    redirect: 'manual'
                });

                paginationButtons.forEach(btn => {
                    btn.classList.remove('disabled');
                    btn.disabled = false;
                });

                if (response.status === 0 || (response.status >= 300 && response.status < 400)) {
                    throw new Error('Session expired. Please log in again.');
                }

                if (!response.ok) {
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        const errorData = await response.json();
                        if (errorData.message === 'Unauthorized') {
                            throw new Error('Session expired. Please log in again.');
                        }
                        throw new Error(errorData.message || 'Failed to fetch products');
                    } else {
                        const text = await response.text();
                        console.error('Non-JSON response received:', text);
                        throw new Error('Unexpected response from server: Expected JSON, received HTML');
                    }
                }

                if (!response.headers.get('content-type')?.includes('application/json')) {
                    const text = await response.text();
                    console.error('Non-JSON response received:', text);
                    throw new Error('Server did not return JSON');
                }

                const data = await response.json();

                if (!data.totalPages || data.currentPage > data.totalPages) {
                    throw new Error('Invalid pagination data from server');
                }

                // Update table body
                const tbody = document.getElementById('productTableBody');
                tbody.innerHTML = '';

                if (data.products.length === 0) {
                    const row = document.createElement('tr');
                    const cell = document.createElement('td');
                    cell.colSpan = 7;
                    cell.style.textAlign = 'center';
                    cell.textContent = 'No products found';
                    row.appendChild(cell);
                    tbody.appendChild(row);
                } else {
                    data.products.forEach(product => {
                        const row = document.createElement('tr');

                        // Image
                        const imageCell = document.createElement('td');
                        if (product.productImage && product.productImage.length > 0) {
                            const img = document.createElement('img');
                            img.src = `/uploads/product-images/${product.productImage[0]}`;
                            img.className = 'product-img';
                            img.alt = product.productName || 'Product Image';
                            imageCell.appendChild(img);
                        } else {
                            const div = document.createElement('div');
                            div.className = 'product-img bg-light d-flex align-items-center justify-content-center';
                            const icon = document.createElement('i');
                            icon.className = 'bi bi-image text-muted';
                            div.appendChild(icon);
                            imageCell.appendChild(div);
                        }
                        row.appendChild(imageCell);

                        // Name (with validation)
                        const nameCell = document.createElement('td');
                        const name = product.productName || 'N/A';
                        nameCell.textContent = validateProductName(name) ? name : 'Invalid Name';
                        if (!validateProductName(name)) {
                            nameCell.style.color = 'red';
                        }
                        row.appendChild(nameCell);

                        // Category
                        const categoryCell = document.createElement('td');
                        categoryCell.textContent = product.category && product.category.name ? product.category.name : 'N/A';
                        row.appendChild(categoryCell);

                        // Price
                        const priceCell = document.createElement('td');
                        priceCell.textContent = product.price ? `$${product.price.toFixed(2)}` : '$0.00';
                        row.appendChild(priceCell);

                        // Quantity
                        const quantityCell = document.createElement('td');
                        quantityCell.textContent = product.quantity ?? '0';
                        row.appendChild(quantityCell);

                        // Status
                        const statusCell = document.createElement('td');
                        const statusSpan = document.createElement('span');
                        statusSpan.className = `status-${product.status.toLowerCase().replace(' ', '-')}`;
                        const statusIcon = document.createElement('i');
                        statusIcon.className = 'bi bi-circle-fill';
                        statusSpan.appendChild(statusIcon);
                        statusSpan.appendChild(document.createTextNode(` ${product.status}`));
                        statusCell.appendChild(statusSpan);
                        if (product.isBlocked) {
                            const badge = document.createElement('span');
                            badge.className = 'badge bg-danger ms-2';
                            badge.textContent = 'Blocked (Hidden from users)';
                            statusCell.appendChild(badge);
                        }
                        row.appendChild(statusCell);

                        // Actions
                        const actionCell = document.createElement('td');
                        actionCell.className = 'action-btns';

                        const editLink = document.createElement('a');
                        editLink.href = `/admin/edit-product/${product._id}`; // Fixed: Use path parameter
                        editLink.className = 'btn btn-sm btn-outline-primary me-1';
                        const editIcon = document.createElement('i');
                        editIcon.className = 'bi bi-pencil';
                        editLink.appendChild(editIcon);
                        actionCell.appendChild(editLink);

                        const toggleLink = document.createElement('a');
                        toggleLink.href = `/admin/block-product/${product._id}`; // Fixed: Use correct route
                        toggleLink.className = `btn btn-sm ${product.isBlocked ? 'btn-success' : 'btn-warning'} me-1`;
                        toggleLink.title = product.isBlocked ? 'Unblock Product' : 'Block Product';
                        const toggleIcon = document.createElement('i');
                        toggleIcon.className = `bi ${product.isBlocked ? 'bi-check-circle' : 'bi-slash-circle'}`;
                        toggleLink.appendChild(toggleIcon);
                        toggleLink.appendChild(document.createTextNode(product.isBlocked ? ' Unblock' : ' Block'));
                        actionCell.appendChild(toggleLink);

                        const deleteBtn = document.createElement('button');
                        deleteBtn.className = 'btn btn-sm btn-outline-danger';
                        deleteBtn.onclick = () => deleteProduct(product._id);
                        const deleteIcon = document.createElement('i');
                        deleteIcon.className = 'bi bi-trash';
                        deleteBtn.appendChild(deleteIcon);
                        deleteBtn.appendChild(document.createTextNode(' Delete'));
                        actionCell.appendChild(deleteBtn);

                        row.appendChild(actionCell);
                        tbody.appendChild(row);
                    });
                }

                // Update pagination controls
                const pagination = document.getElementById('paginationControls');
                if (!pagination) return;

                pagination.innerHTML = '';

                const prevBtn = document.createElement('button');
                prevBtn.className = `pagination-btn ${data.currentPage === 1 ? 'disabled' : ''}`;
                prevBtn.disabled = data.currentPage === 1;
                prevBtn.onclick = () => loadPage(Math.max(1, data.currentPage - 1));
                prevBtn.textContent = 'Previous';
                pagination.appendChild(prevBtn);

                for (let i = 1; i <= data.totalPages; i++) {
                    const pageBtn = document.createElement('button');
                    pageBtn.className = `pagination-btn ${data.currentPage === i ? 'active' : ''}`;
                    pageBtn.onclick = () => loadPage(i);
                    pageBtn.textContent = i;
                    pagination.appendChild(pageBtn);
                }

                const nextBtn = document.createElement('button');
                nextBtn.className = `pagination-btn ${data.currentPage === data.totalPages ? 'disabled' : ''}`;
                nextBtn.disabled = data.currentPage === data.totalPages;
                nextBtn.onclick = () => loadPage(Math.min(data.totalPages, data.currentPage + 1));
                nextBtn.textContent = 'Next';
                pagination.appendChild(nextBtn);
            } catch (error) {
                const paginationButtons = document.querySelectorAll('.pagination-btn');
                paginationButtons.forEach(btn => {
                    btn.classList.remove('disabled');
                    btn.disabled = false;
                });
                console.error('Error loading page:', error);
                if (error.message.includes('Session expired')) {
                    alert(error.message);
                    window.location.href = '/admin/login';
                } else {
                    alert(error.message || 'Failed to load products. Please try again.');
                }
            }
        }

        // Delete product via AJAX
        async function deleteProduct(productId) {
            if (!confirm('Are you sure you want to delete this product?')) return;

            try {
                const deleteBtn = event.currentTarget;
                deleteBtn.disabled = true;
                deleteBtn.style.opacity = '0.5';

                const response = await fetch('/admin/delete-product', { // Fixed: Use correct route
                    method: 'DELETE',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ id: productId })
                });

                deleteBtn.disabled = false;
                deleteBtn.style.opacity = '1';

                if (!response.ok) {
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        const errorData = await response.json();
                        throw new Error(errorData.message || 'Failed to delete product');
                    } else {
                        throw new Error('Unexpected response from server');
                    }
                }

                const data = await response.json();
                alert(data.message || 'Product deleted successfully');

                // Reload the current page to reflect the deletion
                loadPage(<%= currentPage || 1 %>);
            } catch (error) {
                deleteBtn.disabled = false;
                deleteBtn.style.opacity = '1';
                console.error('Error deleting product:', error);
                alert(error.message || 'An error occurred while deleting the product');
            }
        }
    </script>
</body>
</html>