<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Edit Perfume Product - Admin</title>
  <!-- Bootstrap 5 CSS CDN -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
  <style>
    .error-message {
      color: #dc3545;
      font-size: 0.875rem;
      margin-top: 0.25rem;
    }
    .preview-image {
      max-width: 100px;
      max-height: 100px;
      object-fit: cover;
      margin-top: 0.5rem;
    }
    .image-placeholder {
      border: 2px dashed #6c757d;
      padding: 1rem;
      text-align: center;
      margin-top: 0.5rem;
      color: #6c757d;
    }
    .custom-file-input::-webkit-file-upload-button {
      background-color: #e9ecef;
      border: none;
      padding: 0.375rem 0.75rem;
      border-radius: 0.25rem;
      cursor: pointer;
    }
    .custom-file-input:hover::-webkit-file-upload-button {
      background-color: #dee2e6;
    }
  </style>
</head>
<body class="bg-light">
  <div class="container py-5">
    <h1 class="display-6 fw-bold text-dark mb-4">Edit Perfume Product</h1>

    <% if (error) { %>
      <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <%= decodeURIComponent(error).replace(/\+/g, ' ') %>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      </div>
    <% } %>

    <form id="editProductForm" action="/admin/edit-product/<%= product._id %>" method="POST" enctype="multipart/form-data" class="bg-white p-4 rounded shadow">
      <!-- Product Name -->
      <div class="mb-3">
        <label for="productName" class="form-label">Perfume Name</label>
        <input
          type="text"
          id="productName"
          name="productName"
          value="<%= product.productName %>"
          required
          class="form-control"
        >
        <div id="productNameError" class="error-message d-none">Perfume name is required.</div>
      </div>

      <!-- Description -->
      <div class="mb-3">
        <label for="description" class="form-label">Description</label>
        <textarea
          id="description"
          name="description"
          rows="4"
          required
          class="form-control"
        ><%= product.description %></textarea>
        <div id="descriptionError" class="error-message d-none">Description is required.</div>
      </div>

      <!-- Category -->
      <div class="mb-3">
        <label for="category" class="form-label">Category</label>
        <select
          id="category"
          name="category"
          required
          class="form-select"
        >
          <option value="" disabled>Select a category</option>
          <% categories.forEach(cat => { %>
            <option value="<%= cat.name %>" <%= product.category.name === cat.name ? 'selected' : '' %>>
              <%= cat.name %>
            </option>
          <% }) %>
        </select>
        <div id="categoryError" class="error-message d-none">Please select a category.</div>
      </div>

      <!-- Price -->
      <div class="row">
        <div class="col-md-6 mb-3">
          <label for="price" class="form-label">Regular Price (₹)</label>
          <input
            type="number"
            id="price"
            name="price"
            value="<%= product.price %>"
            min="0"
            step="0.01"
            required
            class="form-control"
          >
          <div id="priceError" class="error-message d-none">Price must be a positive number.</div>
        </div>
        <div class="col-md-6 mb-3">
          <label for="salePrice" class="form-label">Sale Price (₹) - Optional</label>
          <input
            type="number"
            id="salePrice"
            name="salePrice"
            value="<%= product.salePrice || '' %>"
            min="0"
            step="0.01"
            class="form-control"
          >
          <div id="salePriceError" class="error-message d-none">Sale price must be less than regular price.</div>
          <small class="text-muted">Leave empty if no sale price</small>
        </div>
      </div>

      <!-- Discount and Quantity -->
      <div class="row">
        <div class="col-md-6 mb-3">
          <label for="discount" class="form-label">Discount Percentage (%) - Optional</label>
          <input
            type="number"
            id="discount"
            name="discount"
            value="<%= product.discount || 0 %>"
            min="0"
            max="100"
            class="form-control"
          >
          <div id="discountError" class="error-message d-none">Discount must be between 0 and 100.</div>
          <small class="text-muted">Enter 0-100 for discount percentage</small>
        </div>
        <div class="col-md-6 mb-3">
          <label for="quantity" class="form-label">Quantity</label>
          <input
            type="number"
            id="quantity"
            name="quantity"
            value="<%= product.quantity %>"
            min="0"
            required
            class="form-control"
          >
          <div id="quantityError" class="error-message d-none">Quantity must be a non-negative number.</div>
        </div>
      </div>

      <!-- Image Uploads -->
      <div class="mb-3">
        <label class="form-label mb-2">Product Images (At least 3 required, JPG/PNG only)</label>
        <p class="text-muted small">You can update individual images by selecting new files below. If you don't select a new file, the existing image will be kept.</p>
        <% for (let i = 1; i <= 4; i++) { %>
          <div class="mb-3">
            <label for="image<%= i %>" class="form-label">Image <%= i %></label>
            <% if (product.productImage[i-1]) { %>
              <img
                src="/uploads/product-images/<%= product.productImage[i-1] %>"
                alt="Current Image <%= i %>"
                class="preview-image"
              >
            <% } else { %>
              <div class="image-placeholder">No image uploaded</div>
            <% } %>
            <input
              type="file"
              id="image<%= i %>"
              name="image<%= i %>"
              accept="image/jpeg,image/png"
              class="form-control custom-file-input mt-2"
            >
            <div id="image<%= i %>Error" class="error-message d-none">Please upload a valid JPG or PNG image.</div>
          </div>
        <% } %>
        <div id="imageCountError" class="error-message d-none">At least 3 images are required.</div>
      </div>

      <!-- Submit Button -->
      <div class="d-flex justify-content-between">
        <a href="/admin/products" class="btn btn-secondary">
          Back to Products
        </a>
        <button
          type="submit"
          class="btn btn-primary"
        >
          Update Perfume
        </button>
      </div>
    </form>
  </div>

  <!-- Bootstrap 5 JS CDN (with Popper.js) -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
  <script>
    document.getElementById('editProductForm').addEventListener('submit', function (e) {
      let isValid = true;
      const errors = document.querySelectorAll('.error-message');
      errors.forEach(error => error.classList.add('d-none'));

      // Validate Product Name
      const productName = document.getElementById('productName').value.trim();
      if (!productName) {
        document.getElementById('productNameError').classList.remove('d-none');
        isValid = false;
      }

      // Validate Description
      const description = document.getElementById('description').value.trim();
      if (!description) {
        document.getElementById('descriptionError').classList.remove('d-none');
        isValid = false;
      }

      // Validate Category
      const category = document.getElementById('category').value;
      if (!category) {
        document.getElementById('categoryError').classList.remove('d-none');
        isValid = false;
      }

      // Validate Price
      const price = parseFloat(document.getElementById('price').value);
      if (isNaN(price) || price < 0) {
        document.getElementById('priceError').classList.remove('d-none');
        isValid = false;
      }

      // Validate Sale Price
      const salePrice = document.getElementById('salePrice').value.trim();
      if (salePrice && (isNaN(parseFloat(salePrice)) || parseFloat(salePrice) < 0)) {
        document.getElementById('salePriceError').classList.remove('d-none');
        isValid = false;
      } else if (salePrice && !isNaN(price) && parseFloat(salePrice) >= price) {
        document.getElementById('salePriceError').classList.remove('d-none');
        isValid = false;
      }

      // Validate Discount
      const discount = document.getElementById('discount').value.trim();
      if (discount && (isNaN(parseFloat(discount)) || parseFloat(discount) < 0 || parseFloat(discount) > 100)) {
        document.getElementById('discountError').classList.remove('d-none');
        isValid = false;
      }

      const quantity = parseInt(document.getElementById('quantity').value);
      if (isNaN(quantity) || quantity < 0) {
        document.getElementById('quantityError').classList.remove('d-none');
        isValid = false;
      }

       const productImages = JSON.parse('<%- JSON.stringify(product && product.productImage ? product.productImage : []) %>');
  const imageInputs = [1, 2, 3, 4].map(i => document.getElementById(`image${i}`)).filter(input => input !== null);
  const existingImages = Array.isArray(productImages) ? productImages.length : 0;
  let newImageCount = 0;

      imageInputs.forEach((input, index) => {
        if (input.files.length > 0) {
          const file = input.files[0];
          const validTypes = ['image/jpeg', 'image/png'];
          if (!validTypes.includes(file.type)) {
            document.getElementById(`image${index + 1}Error`).classList.remove('d-none');
            isValid = false;
          } else {
            newImageCount++;
          }
        }
      });


      const replacedImages = imageInputs.reduce((count, input, idx) => {
        return count + (input.files.length > 0 && productImages[idx] ? 1 : 0);
      }, 0);
      // We'll handle the image count validation on the server side
      // No need to validate here as we're keeping existing images if not replaced

      if (!isValid) {
        e.preventDefault();
      }
    });

    // Preview new images
    [1, 2, 3, 4].forEach(i => {
      const input = document.getElementById(`image${i}`);
      if (input) {
        input.addEventListener('change', function () {
          const file = this.files[0];
          if (file) {
            const reader = new FileReader();
            reader.onload = function (e) {
              let img = input.previousElementSibling;
              if (img.tagName !== 'IMG') {
                img = document.createElement('img');
                img.className = 'preview-image';
                input.previousElementSibling.replaceWith(img);
              }
              img.src = e.target.result;
            };
            reader.readAsDataURL(file);
          }
        });
      }
    });
  </script>
</body>
</html>