<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>My Profile | Luxe Scents</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
    body {
      background-color: #f8f9fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .navbar {
      background-color: #000000;
      padding: 1rem 2rem;
      border-bottom: 1px solid #090909;
    }
    .navbar-brand {
      font-weight: 700;
      font-size: 1.8rem;
      color: #ffffff !important;
      letter-spacing: 1px;
    }
    .nav-link {
      color: #e0e0e0 !important;
      font-weight: 500;
      margin: 0 1rem;
      transition: color 0.3s;
    }
    .nav-link:hover {
      color: #bb86fc !important;
    }
    .profile-container {
      max-width: 800px;
      margin: 2rem auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    .profile-header {
      background: linear-gradient(135deg, #6200ea, #bb86fc);
      color: white;
      padding: 2rem;
      text-align: center;
      position: relative;
    }
    .profile-image {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      border: 4px solid white;
      object-fit: cover;
      margin-bottom: 1rem;
    }
    .profile-body {
      padding: 2rem;
    }
    .info-section {
      margin-bottom: 2rem;
    }
    .info-section h5 {
      color: #6200ea;
      border-bottom: 2px solid #6200ea;
      padding-bottom: 0.5rem;
      margin-bottom: 1rem;
    }
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem 0;
      border-bottom: 1px solid #eee;
    }
    .info-item:last-child {
      border-bottom: none;
    }
    .info-label {
      font-weight: 600;
      color: #555;
    }
    .info-value {
      color: #333;
    }
    .btn-primary {
      background-color: #6200ea;
      border-color: #6200ea;
      border-radius: 25px;
      padding: 0.5rem 1.5rem;
      font-weight: 500;
      transition: all 0.3s;
    }
    .btn-primary:hover {
      background-color: #5300d1;
      transform: translateY(-2px);
    }
    .btn-outline-primary {
      color: #6200ea;
      border-color: #6200ea;
      border-radius: 25px;
      padding: 0.5rem 1.5rem;
      font-weight: 500;
      transition: all 0.3s;
    }
    .btn-outline-primary:hover {
      background-color: #6200ea;
      border-color: #6200ea;
      transform: translateY(-2px);
    }
    .alert {
      border-radius: 10px;
      border: none;
    }
    .alert-success {
      background-color: #d4edda;
      color: #155724;
    }
    .alert-danger {
      background-color: #f8d7da;
      color: #721c24;
    }
    .action-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      margin-top: 2rem;
    }
    @media (max-width: 768px) {
      .profile-container {
        margin: 1rem;
      }
      .action-buttons {
        flex-direction: column;
      }
    }
  </style>
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
      <a class="navbar-brand" href="/">LUXE SCENTS</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <a class="nav-link" href="/">Home</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/shop">Shop</a>
          </li>
          <li class="nav-item">
            <a class="nav-link active" href="/profile">Profile</a>
          </li>
        </ul>
        <div class="navbar-icons">
          <div class="dropdown">
            <a href="#" class="dropdown-toggle text-white" role="button" data-bs-toggle="dropdown">
              <i class="fas fa-user-circle"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
              <li><a class="dropdown-item" href="/profile">My Profile</a></li>
              <li><a class="dropdown-item" href="#" onclick="confirmLogout(event)">Logout</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- Profile Container -->
  <div class="profile-container">
    <!-- Profile Header -->
    <div class="profile-header">
      <% if (user.profileImage) { %>
        <img src="/uploads/profile-images/<%= user.profileImage %>" alt="Profile Image" class="profile-image">
      <% } else { %>
        <div class="profile-image d-flex align-items-center justify-content-center" style="background-color: rgba(255,255,255,0.2);">
          <i class="fas fa-user fa-3x"></i>
        </div>
      <% } %>
      <h2><%= user.name || 'User' %></h2>
      <p class="mb-0"><%= user.email %></p>
    </div>

    <!-- Profile Body -->
    <div class="profile-body">
      <!-- Success/Error Messages -->
      <% if (message) { %>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
          <%= message %>
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      <% } %>
      <% if (error) { %>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
          <%= error %>
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      <% } %>

      <!-- Personal Information -->
      <div class="info-section">
        <h5><i class="fas fa-user me-2"></i>Personal Information</h5>
        <div class="info-item">
          <span class="info-label">Full Name</span>
          <span class="info-value"><%= user.name || 'Not provided' %></span>
        </div>
        <div class="info-item">
          <span class="info-label">Email</span>
          <span class="info-value"><%= user.email %></span>
        </div>
        <div class="info-item">
          <span class="info-label">Phone</span>
          <span class="info-value"><%= user.phone || 'Not provided' %></span>
        </div>
        <div class="info-item">
          <span class="info-label">Member Since</span>
          <span class="info-value"><%= new Date(user.createdAt).toLocaleDateString() %></span>
        </div>
      </div>

      <!-- Address Information -->
      <div class="info-section">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
          <h5 style="margin: 0;"><i class="fas fa-map-marker-alt me-2"></i>Address Information</h5>
          <a href="/addresses" class="btn btn-outline-primary btn-sm" style="border-radius: 20px; padding: 0.4rem 1rem;">
            <i class="fas fa-cog me-1"></i>Manage Addresses
          </a>
        </div>
        <div class="info-item">
          <span class="info-label">Primary Address</span>
          <span class="info-value">
            <% if (user.address?.street) { %>
              <%= user.address.street %>, <%= user.address.city || '' %>, <%= user.address.state || '' %> - <%= user.address.zipCode || '' %>
            <% } else { %>
              <a href="/addresses/add" class="text-primary">Add your first address</a>
            <% } %>
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">Address Management</span>
          <span class="info-value">
            <a href="/addresses" class="text-primary">View all addresses</a> |
            <a href="/addresses/add" class="text-primary">Add new address</a>
          </span>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <a href="/profile/edit" class="btn btn-primary">
          <i class="fas fa-edit me-2"></i>Edit Profile
        </a>
        <a href="/profile/change-password" class="btn btn-outline-primary">
          <i class="fas fa-lock me-2"></i>Change Password
        </a>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    function confirmLogout(event) {
      event.preventDefault();
      Swal.fire({
        title: 'Are you sure?',
        text: 'You will be logged out of your account',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#6200ea',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, logout'
      }).then((result) => {
        if (result.isConfirmed) {
          window.location.href = '/logout';
        }
      });
    }
  </script>
</body>
</html>
